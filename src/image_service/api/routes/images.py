"""
Image generation API routes
"""

# Global imports
import os
import uuid
import requests
import tempfile
from uuid import UUID
from typing import List, Optional
from loguru import logger
from pydantic import BaseModel
from urllib.parse import urlparse
from sqlalchemy import and_, select
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks

BASE_URL = os.getenv("BASE_URL", "http://localhost:8000")

# Local imports
from src.shared.utils.s3_client import S3Client
from src.shared.core.auth import get_current_user_info
from src.shared.core.dependencies import require_auth
from src.image_service.image_prompt_gen import ImagePromptGenerator
from src.shared.utils.prompt_cleaner import format_prompt_for_display
from src.image_service.services.image_generator import ImageGenerationService
from src.image_service.services import AgenticImageService, BatchImageGenerationService
from src.shared.config.database import get_database_session, get_sync_db
from src.shared.config.settings import settings
from src.shared.models.database_models import Scene, Script, ImageGeneration, ImageAsset
from src.image_service.schemas.requests import (
    GenerateImagesForScenesRequest,
    UpdatePromptAndRegenerateRequest,
    RegenerateImagesRequest,
)
from src.image_service.schemas.responses import (
    ScriptImageGenerationResponse,
    ImageGenerationResponse,
)


# Response models for image generation status
class ImageGenerationSceneStatusResponse(BaseModel):
    """Response model for image generation status of a single scene"""

    id: str
    scene_number: int
    generation_status: str
    presigned_url: Optional[str] = None
    error_message: Optional[str] = None
    image_prompt: Optional[str] = None


class ImageGenerationScriptStatusResponse(BaseModel):
    """Response model for image generation status of all scenes in a script"""

    script_id: str
    scenes: List[ImageGenerationSceneStatusResponse]


router = APIRouter(prefix="/images", tags=["Image Generation"])

image_generator = ImageGenerationService()
prompt_generator = ImagePromptGenerator()
agentic_service = AgenticImageService()

# At the top, set S3 bucket and region from env, with fallback and error if missing
S3_BUCKET = os.getenv("AWS_S3_BUCKET", "assets-vidflux")
S3_REGION = os.getenv("AWS_DEFAULT_REGION", "us-east-2")
s3_client = S3Client(S3_REGION)


def save_image_from_url(image_url, scene_id):
    # Download image to temp file, then upload to S3
    response = requests.get(image_url)
    if response.status_code != 200:
        raise Exception(f"Failed to download image from {image_url}")
    with tempfile.NamedTemporaryFile(delete=False, suffix=".jpg") as tmp_file:
        tmp_file.write(response.content)
        temp_path = tmp_file.name
    image_id = str(uuid.uuid4())
    s3_key = f"Vidflux-Assets/image-assets/scene_{scene_id}/image_{image_id}.jpg"
    s3_url = s3_client.upload_file(bucket=S3_BUCKET, key=s3_key, file_path=temp_path)
    if os.path.exists(temp_path):
        os.remove(temp_path)
    return s3_url, s3_key, image_id


def delete_previous_images_for_scene(scene_id: str, org_id: str, db_session):
    """
    Delete previous images for a scene from S3 and mark them as inactive in database
    """
    try:
        # Get all active images for this scene
        previous_images = (
            db_session.query(ImageAsset)
            .filter(
                and_(
                    ImageAsset.scene_id == scene_id,
                    ImageAsset.org_id == org_id,
                    ImageAsset.status == "active",
                )
            )
            .all()
        )

        deleted_count = 0
        for image in previous_images:
            try:
                # Extract S3 key from the S3 URL
                if image.s3_url and ".amazonaws.com/" in image.s3_url:
                    # Parse the S3 URL to get the key
                    parsed = urlparse(image.s3_url)
                    s3_key = parsed.path.lstrip("/")

                    # Delete from S3
                    if s3_client.delete_file(S3_BUCKET, s3_key):
                        logger.info(f"✅ Deleted previous image from S3: {s3_key}")
                        deleted_count += 1
                    else:
                        logger.warning(f"⚠️ Previous image not found in S3: {s3_key}")
                else:
                    logger.warning(f"⚠️ Image {image.asset_id} has no valid S3 URL: {image.s3_url}")

                # Mark as inactive in database
                image.status = "inactive"
                image.deleted_at = datetime.utcnow()

            except Exception as e:
                logger.error(f"❌ Error deleting previous image {image.asset_id}: {str(e)}")

        # Commit the database changes
        db_session.commit()

        if deleted_count > 0:
            logger.info(f"✅ Deleted {deleted_count} previous images for scene {scene_id}")
        else:
            logger.info(f"ℹ️ No previous images found for scene {scene_id}")

        return deleted_count

    except Exception as e:
        logger.error(f"❌ Error in delete_previous_images_for_scene: {str(e)}")
        raise


@router.post("/generate", response_model=ScriptImageGenerationResponse)
async def generate_images_for_scenes(
    request: GenerateImagesForScenesRequest,
    background_tasks: BackgroundTasks,
    user_info: tuple = Depends(get_current_user_info),
    db: AsyncSession = Depends(get_database_session),
):
    """
    Generate images for a list of scenes (one image per scene)
    - **scene_ids**: List of scene UUIDs
    - **aspect_ratio**: Optional aspect ratio (default: 16:9)
    - **include_brand**: Whether to include brand integration (default: True)
    """
    org_id, user_id, email = user_info

    if not request.scene_ids:
        raise HTTPException(status_code=400, detail="No scene_ids provided")

    # Fetch all scenes and validate
    valid_scenes = []
    scene_generations = []
    generation_ids = []

    for scene_id in request.scene_ids:
        scene_stmt = select(Scene).where(Scene.id == scene_id)
        scene_result = await db.execute(scene_stmt)
        scene = scene_result.scalar_one_or_none()
        if not scene:
            logger.warning(f"⚠️ Scene {scene_id} not found, skipping")
            continue

        valid_scenes.append(scene)
        generation_id = str(uuid.uuid4())
        generation_ids.append(generation_id)

        # Create image generation record (sync for background task)
        try:
            sync_db = next(get_sync_db())
            try:
                image_generation = ImageGeneration(
                    id=generation_id,
                    org_id=org_id,
                    user_id=user_id,
                    scene_id=str(scene.id),
                    script_id=str(scene.script_id),
                    aspect_ratio=request.aspect_ratio or "16:9",
                    include_brand=request.include_brand,
                    status="pending",
                    progress=0,
                    created_at=datetime.utcnow(),
                )
                sync_db.add(image_generation)
                sync_db.commit()
                sync_db.refresh(image_generation)
            finally:
                sync_db.close()
        except Exception as e:
            logger.warning(
                f"⚠️ Could not create image generation record for scene {scene.id}: {str(e)}"
            )

        scene_generations.append(
            ImageGenerationResponse(
                generation_id=generation_id,
                scene_id=str(scene.id),
                status="pending",
                progress=0,
                message="Image generation started",
            )
        )

    if not valid_scenes:
        raise HTTPException(status_code=400, detail="No valid scenes found")

    # Start single background task for all scenes (optimized with batch processing)
    logger.info(f"🚀 Starting image generation for {len(valid_scenes)} scenes")
    background_tasks.add_task(
        _generate_images_batch_background,
        generation_ids,
        [str(scene.id) for scene in valid_scenes],
        request.aspect_ratio or "16:9",
        request.include_brand,
        org_id,
        user_id,
        settings.max_concurrent_images,
    )

    return ScriptImageGenerationResponse(script_id="multiple", scene_generations=scene_generations)


@router.post("/regenerate", response_model=ScriptImageGenerationResponse)
async def regenerate_images_for_scenes(
    request: RegenerateImagesRequest,
    background_tasks: BackgroundTasks,
    user_info: tuple = Depends(get_current_user_info),
    db: AsyncSession = Depends(get_database_session),
):
    """
    Regenerate images for a list of scenes (one image per scene)
    - **scene_ids**: List of scene UUIDs
    - **aspect_ratio**: Optional aspect ratio (default: 16:9)
    - **include_brand**: Whether to include brand integration (default: True)
    """
    org_id, user_id, email = user_info

    if not request.scene_ids:
        raise HTTPException(status_code=400, detail="No scene_ids provided")

    # Fetch all scenes and validate
    valid_scenes = []
    scene_generations = []
    generation_ids = []

    for scene_id in request.scene_ids:
        scene_stmt = select(Scene).where(Scene.id == scene_id)
        scene_result = await db.execute(scene_stmt)
        scene = scene_result.scalar_one_or_none()
        if not scene:
            logger.warning(f"⚠️ Scene {scene_id} not found, skipping")
            continue

        valid_scenes.append(scene)
        generation_id = str(uuid.uuid4())
        generation_ids.append(generation_id)

        # Create image generation record (sync for background task)
        try:
            sync_db = next(get_sync_db())
            try:
                image_generation = ImageGeneration(
                    id=generation_id,
                    org_id=org_id,
                    user_id=user_id,
                    scene_id=str(scene.id),
                    script_id=str(scene.script_id),
                    aspect_ratio=request.aspect_ratio or "16:9",
                    include_brand=request.include_brand,
                    status="pending",
                    progress=0,
                    is_regeneration=True,
                    created_at=datetime.utcnow(),
                )
                sync_db.add(image_generation)
                sync_db.commit()
                sync_db.refresh(image_generation)
            finally:
                sync_db.close()
        except Exception as e:
            logger.warning(
                f"⚠️ Could not create image regeneration record for scene {scene.id}: {str(e)}"
            )

        scene_generations.append(
            ImageGenerationResponse(
                generation_id=generation_id,
                scene_id=str(scene.id),
                status="pending",
                progress=0,
                message="Image regeneration started",
            )
        )

    if not valid_scenes:
        raise HTTPException(status_code=400, detail="No valid scenes found")

    # Start single background task for all scenes (optimized with batch processing)
    logger.info(f"🔄 Starting image regeneration for {len(valid_scenes)} scenes")
    background_tasks.add_task(
        _generate_images_batch_background,
        generation_ids,
        [str(scene.id) for scene in valid_scenes],
        request.aspect_ratio or "16:9",
        request.include_brand,
        org_id,
        user_id,
        settings.max_concurrent_images,
    )

    return ScriptImageGenerationResponse(script_id="multiple", scene_generations=scene_generations)


@router.get("/{script_id}")
async def get_script_images(
    script_id: str,
    user_info: tuple = Depends(get_current_user_info),
    db: AsyncSession = Depends(get_database_session),
):
    """
    Get all images for all scenes in a script
    - **script_id**: UUID of the script
    Returns: list of { scene_id, images: [ {id, url, selected, created_at} ] }
    """
    org_id, user_id, email = user_info
    # Get all scenes for this script
    scenes_stmt = select(Scene).where(and_(Scene.script_id == script_id, Scene.status == "active"))
    scenes_result = await db.execute(scenes_stmt)
    scenes = scenes_result.scalars().all()
    if not scenes:
        return []
    # For each scene, get all images
    result = []
    for scene in scenes:
        images_stmt = select(ImageAsset).where(
            and_(ImageAsset.scene_id == str(scene.id), ImageAsset.status == "active")
        )
        print(f"Images Statement: {images_stmt}")
        print(f"Scene ID: {scene.id}")
        images_result = await db.execute(images_stmt)
        images = images_result.scalars().all()
        image_objs = []
        print(f"Images: {images}")
        for img in images:
            s3_url = img.s3_url
            url = None
            if s3_url and isinstance(s3_url, str):
                if ".amazonaws.com/" in s3_url:
                    key = f"Vidflux-Assets/image-assets/scene_{scene.id}/image_{img.asset_id}.jpg"
                    url = s3_client.get_presigned_url(S3_BUCKET, key, timedelta(hours=1))
                else:
                    url = s3_url
            image_objs.append(
                {
                    "id": img.asset_id,
                    "url": url,
                    "selected": img.is_selected,
                    "created_at": img.created_at,
                }
            )
        result.append({"scene_id": str(scene.id), "images": image_objs})
    return result


@router.get("/status/{script_id}", response_model=ImageGenerationScriptStatusResponse)
async def get_image_generation_status(
    script_id: str,
    user_info: tuple = Depends(require_auth),
    db: AsyncSession = Depends(get_database_session),
):
    """
    Get image generation status for all scenes in a script
    - **script_id**: UUID of the script
    Returns: Current image generation status of all scenes (non-blocking, immediate response)
    """
    org_id, user_id, email = user_info

    # Validate script exists and belongs to user
    script_stmt = select(Script).where(
        and_(Script.id == script_id, Script.org_id == org_id, Script.user_id == UUID(user_id))
    )
    script_result = await db.execute(script_stmt)
    script = script_result.scalar_one_or_none()

    if not script:
        raise HTTPException(status_code=404, detail="Script not found")

    # Query all scenes for this script with scene info
    scenes_stmt = (
        select(Scene.id, Scene.scene_number, Scene.image_prompt)  # Include the image_prompt field
        .where(Scene.script_id == script_id)
        .order_by(Scene.scene_number)
    )

    scenes_result = await db.execute(scenes_stmt)
    scenes = scenes_result.all()

    # Format response
    scenes_data = []
    for scene in scenes:
        presigned_url = None
        generation_status = "pending"
        error_message = None
        image_prompt = None

        # Check for the latest image generation for this scene
        image_gen_stmt = (
            select(ImageGeneration)
            .where(
                and_(
                    ImageGeneration.scene_id == scene.id,
                    ImageGeneration.org_id == org_id,
                    ImageGeneration.user_id == UUID(user_id),
                )
            )
            .order_by(ImageGeneration.created_at.desc())
        )

        image_gen_result = await db.execute(image_gen_stmt)
        image_generation = image_gen_result.scalar()

        if image_generation:
            generation_status = image_generation.status
            error_message = getattr(image_generation, "error_message", None)

            # If completed, check for actual image asset
            if image_generation.status == "completed":
                image_asset_stmt = (
                    select(ImageAsset)
                    .where(
                        and_(
                            ImageAsset.scene_id == scene.id,
                            ImageAsset.org_id == org_id,
                            ImageAsset.status == "active",
                            ImageAsset.s3_url.isnot(None),
                        )
                    )
                    .order_by(ImageAsset.created_at.desc())
                )

                image_asset_result = await db.execute(image_asset_stmt)
                image_asset = image_asset_result.scalar()

                if image_asset and image_asset.s3_url:
                    # Generate presigned URL for the image
                    try:
                        parsed = urlparse(image_asset.s3_url)
                        s3_key = parsed.path.lstrip("/")
                        presigned_url = s3_client.get_presigned_url(
                            S3_BUCKET, s3_key, timedelta(hours=1)
                        )
                    except Exception as e:
                        # If presigned URL generation fails, log but continue
                        logger.warning(f"Failed to generate presigned URL for image: {e}")
                        presigned_url = None
                else:
                    # No image asset found, so status should not be completed
                    generation_status = "pending"

        # Get the generated prompt from the scene data we fetched
        image_prompt = format_prompt_for_display(scene.image_prompt)

        scenes_data.append(
            ImageGenerationSceneStatusResponse(
                id=str(scene.id),
                scene_number=scene.scene_number,
                generation_status=generation_status,
                presigned_url=presigned_url,
                error_message=error_message,
                image_prompt=image_prompt,
            )
        )

    return ImageGenerationScriptStatusResponse(script_id=script_id, scenes=scenes_data)


@router.put("/update-prompt/{scene_id}", response_model=ImageGenerationResponse)
async def update_prompt_and_regenerate(
    scene_id: str,
    request: UpdatePromptAndRegenerateRequest,
    background_tasks: BackgroundTasks,
    user_info: tuple = Depends(get_current_user_info),
    db: AsyncSession = Depends(get_database_session),
):
    """
    Update the prompt for a scene and regenerate the image with the new prompt
    """
    try:
        org_id, user_id, email = user_info

        # Get the scene to validate it exists and belongs to the user
        try:
            scene_uuid = UUID(scene_id)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid scene ID format: {scene_id}",
            )

        scene_query = select(Scene).where(Scene.id == scene_uuid)
        scene_result = await db.execute(scene_query)
        scene = scene_result.scalar_one_or_none()

        if not scene:
            # Check if this might be a script ID instead
            script_check_query = select(Script).where(Script.id == scene_uuid)
            script_check_result = await db.execute(script_check_query)
            script_exists = script_check_result.scalar_one_or_none()

            if script_exists:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"The provided ID '{scene_id}' is a script ID, not a scene ID. Please use a scene ID from the scenes array.",
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Scene with ID '{scene_id}' not found",
                )

        # Get the script to validate user access
        script_query = select(Script).where(Script.id == scene.script_id)
        script_result = await db.execute(script_query)
        script = script_result.scalar_one_or_none()

        if not script:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Script not found")

        # Validate user has access to this script
        if script.user_id != UUID(user_id):
            logger.warning(
                f"User {user_id} attempted to access script {script.id} owned by {script.user_id}"
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You don't have permission to update this scene",
            )

        # Update the scene's image_prompt with the new prompt
        scene.image_prompt = request.updated_prompt
        await db.commit()

        logger.info(f"✅ Updated prompt for scene {scene_id}: {request.updated_prompt[:100]}...")

        # First, delete all previous ImageAsset records for this scene (this will be done in background task)
        # We don't delete them here to avoid breaking the foreign key constraint

        # Get previous image generation records for this scene to get their IDs
        previous_generations_query = select(ImageGeneration).where(
            and_(ImageGeneration.scene_id == UUID(scene_id), ImageGeneration.org_id == org_id)
        )
        previous_generations_result = await db.execute(previous_generations_query)
        previous_generations = previous_generations_result.scalars().all()

        # Store the generation IDs for cleanup in background task
        previous_generation_ids = [str(gen.id) for gen in previous_generations]

        # Create a new image generation record
        generation_id = uuid.uuid4()
        new_generation = ImageGeneration(
            id=generation_id,
            org_id=org_id,
            user_id=user_id,
            scene_id=UUID(scene_id),
            script_id=scene.script_id,
            aspect_ratio=request.aspect_ratio,
            include_brand=request.include_brand,
            status="pending",
            is_regeneration=True,  # This is a regeneration, not initial generation
            created_at=datetime.utcnow(),
        )

        db.add(new_generation)
        await db.commit()

        # Start background task to regenerate the image using the updated prompt
        background_tasks.add_task(
            _generate_images_with_custom_prompt_background,
            str(generation_id),
            scene_id,
            request.updated_prompt,
            request.aspect_ratio,
            request.include_brand,
            org_id,
            str(user_id),
            previous_generation_ids,  # Pass previous generation IDs for cleanup
        )

        logger.info(f"🚀 Started image regeneration for scene {scene_id} with updated prompt")

        return ImageGenerationResponse(
            generation_id=str(generation_id),
            scene_id=scene_id,
            status="pending",
            progress=0,
            message="Image regeneration started with updated prompt",
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"❌ Error updating prompt and regenerating image for scene {scene_id}: {str(e)}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update prompt and regenerate image: {str(e)}",
        )


def _generate_images_batch_background(
    generation_ids: List[str],
    scene_ids: List[str],
    aspect_ratio: str,
    include_brand: bool,
    org_id: str,
    user_id: str,
    max_concurrent: int = 3,
):
    """
    Background task to generate images for multiple scenes in parallel
    """
    import asyncio

    async def run_batch_generation():
        try:
            logger.info(f"🚀 Starting image generation for {len(scene_ids)} scenes")

            # Get database session
            db = next(get_sync_db())

            try:
                # Update all generations to processing
                for generation_id in generation_ids:
                    try:
                        generation = (
                            db.query(ImageGeneration).filter(ImageGeneration.id == generation_id).first()
                        )
                        if generation:
                            generation.status = "processing"
                            generation.progress = 5
                            db.commit()
                    except Exception as e:
                        logger.warning(f"⚠️ Could not update generation {generation_id}: {str(e)}")

                # Get all scenes data
                scenes_data = []
                scenes_by_id = {}

                for scene_id in scene_ids:
                    scene = db.query(Scene).filter(Scene.id == scene_id).first()
                    if scene:
                        scene_data = {
                            "id": str(scene.id),
                            "number": scene.scene_number,
                            "description": scene.description or "",
                            "visual": scene.visual_description or "",
                            "narration": scene.narration or "",
                            "duration": scene.duration or "5s",
                        }
                        scenes_data.append(scene_data)
                        scenes_by_id[str(scene.id)] = scene

                if not scenes_data:
                    raise Exception("No valid scenes found")

                # Get brand info from first scene's script
                first_scene = scenes_by_id[scene_ids[0]]
                script = db.query(Script).filter(Script.id == first_scene.script_id).first()

                # Initialize optimized image generation service
                batch_service = BatchImageGenerationService(max_concurrent_images=max_concurrent)

                # Set brand info if available
                if script and script.brand_name and script.product_name:
                    brand_info = {
                        "brand_name": script.brand_name,
                        "product_name": script.product_name,
                        "brand_description": script.brand_description,
                    }
                    batch_service.set_brand_info(brand_info)

                # Progress callback to update database
                def progress_callback(stage: str, current: int, total: int, message: str):
                    try:
                        progress_percent = int((current / total) * 80) + 10  # 10-90% range
                        for generation_id in generation_ids:
                            generation = (
                                db.query(ImageGeneration).filter(ImageGeneration.id == generation_id).first()
                            )
                            if generation:
                                generation.progress = progress_percent
                                # Use generic messages that don't expose internal batch processing
                                if stage == "planning":
                                    generation.message = "Preparing image generation"
                                elif stage == "generation":
                                    generation.message = "Generating image"
                                else:
                                    generation.message = "Processing image generation"
                                db.commit()
                    except Exception as e:
                        logger.warning(f"⚠️ Could not update progress: {str(e)}")

                # Delete previous images for all scenes
                for scene_id in scene_ids:
                    try:
                        deleted_count = delete_previous_images_for_scene(scene_id, org_id, db)
                        logger.info(f"✅ Deleted {deleted_count} previous images for scene {scene_id}")
                    except Exception as e:
                        logger.warning(f"⚠️ Could not delete previous images for scene {scene_id}: {str(e)}")

                # Run optimized image generation
                results = await batch_service.generate_images_for_scenes(
                    scenes_data=scenes_data,
                    aspect_ratio=aspect_ratio,
                    include_brand=include_brand,
                    progress_callback=progress_callback
                )

                if not results["success"]:
                    raise Exception(f"Image generation failed: {results.get('error')}")

                # Process successful results
                for success_result in results["successful_results"]:
                    scene_id = success_result["scene_id"]
                    scene = scenes_by_id.get(scene_id)

                    if not scene:
                        continue

                    # Find corresponding generation_id
                    generation_id = None
                    scene_index = scene_ids.index(scene_id) if scene_id in scene_ids else -1
                    if 0 <= scene_index < len(generation_ids):
                        generation_id = generation_ids[scene_index]

                    # Update scene with generated prompt
                    try:
                        scene.image_prompt = success_result["prompt"]
                        db.add(scene)
                        db.commit()
                    except Exception as e:
                        logger.warning(f"⚠️ Could not store prompt for scene {scene_id}: {str(e)}")

                    # Save images to database
                    for img in success_result["images"]:
                        try:
                            fal_url = img["url"]
                            s3_url, s3_key, image_id = save_image_from_url(fal_url, scene_id)

                            image_asset = ImageAsset(
                                asset_id=image_id,
                                org_id=org_id,
                                s3_url=s3_url,
                                local_path=None,
                                generation_id=generation_id,
                                scene_id=scene_id,
                                script_id=scene.script_id,
                                is_selected=False,
                                status="active",
                                created_at=datetime.utcnow(),
                            )
                            db.add(image_asset)
                            db.commit()

                            logger.info(f"✅ Saved image for scene {scene_id}: {s3_url}")

                        except Exception as upload_error:
                            logger.error(f"❌ Failed to save image for scene {scene_id}: {str(upload_error)}")

                # Update final status for all generations
                for i, generation_id in enumerate(generation_ids):
                    try:
                        generation = (
                            db.query(ImageGeneration).filter(ImageGeneration.id == generation_id).first()
                        )
                        if generation:
                            scene_id = scene_ids[i] if i < len(scene_ids) else None

                            # Check if this scene was successful
                            scene_successful = any(
                                r["scene_id"] == scene_id for r in results["successful_results"]
                            )

                            if scene_successful:
                                generation.status = "completed"
                                generation.progress = 100
                                generation.message = "Image generation completed successfully"
                            else:
                                generation.status = "failed"
                                generation.progress = 100
                                generation.message = "Image generation failed"

                            db.commit()
                    except Exception as e:
                        logger.warning(f"⚠️ Could not update final status for {generation_id}: {str(e)}")

                logger.success(
                    f"🎉 Image generation completed: {results['successful_generations']}/{results['total_scenes']} successful"
                )

            finally:
                db.close()

        except Exception as e:
            logger.error(f"❌ Image generation failed: {e}")

            # Update all generations to failed status
            db = next(get_sync_db())
            try:
                for generation_id in generation_ids:
                    try:
                        generation = (
                            db.query(ImageGeneration).filter(ImageGeneration.id == generation_id).first()
                        )
                        if generation:
                            generation.status = "failed"
                            generation.progress = 100
                            generation.message = f"Image generation failed: {str(e)}"
                            db.commit()
                    except Exception as update_error:
                        logger.warning(f"⚠️ Could not update failed status for {generation_id}: {str(update_error)}")
            finally:
                db.close()

    # Run the async function
    asyncio.run(run_batch_generation())


def _generate_images_background(
    generation_id: str,
    scene_id: str,
    aspect_ratio: str,
    include_brand: bool,
    org_id: str,
    user_id: str,
):
    """
    Background task to generate images
    """
    try:
        logger.info(f"🎨 Starting background image generation for {generation_id}")

        # Get database session
        db = next(get_sync_db())

        try:
            # Update status to processing
            try:
                generation = (
                    db.query(ImageGeneration).filter(ImageGeneration.id == generation_id).first()
                )

                if not generation:
                    logger.warning(
                        f"⚠️ Generation {generation_id} not found, continuing without status updates"
                    )
                    generation = None
                else:
                    generation.status = "processing"
                    generation.progress = 10
                    db.commit()
            except Exception as e:
                # Handle case where image_generations table doesn't exist yet
                logger.warning(
                    f"⚠️ Could not update generation status (table may not exist): {str(e)}"
                )
                generation = None

            # Get scene data
            scene = db.query(Scene).filter(Scene.id == scene_id).first()
            if not scene:
                raise Exception("Scene not found")

            # Get script for brand info
            script = db.query(Script).filter(Script.id == scene.script_id).first()
            if not script:
                raise Exception("Script not found")

            # Set brand info if available
            if script.brand_name and script.product_name:
                brand_info = {
                    "brand_name": script.brand_name,
                    "product_name": script.product_name,
                    "brand_description": script.brand_description,
                }
                prompt_generator.set_brand_info(brand_info)
                image_generator.set_brand_info(brand_info)

            # Delete previous images for this scene before generating new ones
            try:
                logger.info(f"🗑️ Deleting previous images for scene {scene_id}")
                deleted_count = delete_previous_images_for_scene(scene_id, org_id, db)
                logger.info(f"✅ Deleted {deleted_count} previous images for scene {scene_id}")
            except Exception as e:
                logger.warning(f"⚠️ Could not delete previous images for scene {scene_id}: {str(e)}")
                # Continue with generation even if deletion fails

            # Generate agentic scene plan + prompt
            try:
                if generation:
                    generation.progress = 20
                    db.commit()
            except Exception as e:
                logger.warning(
                    f"⚠️ Could not update generation progress (table may not exist): {str(e)}"
                )

            # Prepare scene data for agentic planner
            scene_data = {
                "number": scene.scene_number,
                "description": scene.description or "",
                "visual": scene.visual_description or "",
                "narration": scene.narration or "",
                "duration": scene.duration or "5s",
            }

            # Configure brand into agentic service if present
            brand_info = None
            if script.brand_name and script.product_name:
                brand_info = {
                    "brand_name": script.brand_name,
                    "product_name": script.product_name,
                    "brand_description": script.brand_description,
                }
                agentic_service.set_brand_info(brand_info)
                image_generator.set_brand_info(brand_info)

            # Run agentic planning
            agentic_outputs = agentic_service.plan_and_generate_prompts([scene_data])
            if not agentic_outputs:
                raise Exception("Agentic planner failed to produce output")

            agentic_scene = agentic_outputs[0]
            image_prompt = agentic_scene.get("image_prompt", "")
            scene_aspect_ratio = agentic_scene.get("aspect_ratio", aspect_ratio)
            includes_brand_flag = bool(agentic_scene.get("includes_brand", include_brand))

            if not image_prompt:
                raise Exception("Agentic output missing image_prompt")
            try:
                # Update the scene with the generated prompt using synchronous session
                scene.image_prompt = image_prompt
                db.add(scene)  # Ensure the scene is tracked by the session
                db.commit()
                logger.info(f"✅ Stored image prompt in scene {scene_id}: {image_prompt[:100]}...")

                # Update generation progress if it exists
                if generation:
                    generation.progress = 30
                    db.add(generation)  # Ensure the generation is tracked by the session
                    db.commit()
            except Exception as e:
                logger.warning(f"⚠️ Could not store generated prompt in scene: {str(e)}")
                db.rollback()  # Rollback on error

            # Generate 1 image (was 3)
            image_results = []
            for i in range(1):
                try:
                    if generation:
                        generation.progress = 30 + (i * 20)  # 30, 50, 70
                        db.commit()
                except Exception as e:
                    logger.warning(
                        f"⚠️ Could not update generation progress (table may not exist): {str(e)}"
                    )

                # Generate single image via decided parameters
                try:
                    result = image_generator.generate_single_image(
                        prompt=image_prompt,
                        scene_data={
                            **scene_data,
                            "includes_brand": includes_brand_flag,
                            "aspect_ratio": scene_aspect_ratio,
                            "original_visual": scene.visual_description or "",
                        },
                        aspect_ratio=scene_aspect_ratio,
                        num_images=1,
                    )
                except Exception as img_error:
                    logger.error(f"❌ Image generation error: {str(img_error)}")
                    result = {"success": False, "error": str(img_error)}

                if result.get("success") and result.get("images"):
                    image_results.append(result)
                else:
                    logger.warning(f"⚠️ Image {i+1} generation failed: {result.get('error')}")

            try:
                if generation:
                    generation.progress = 90
                    db.commit()
            except Exception as e:
                logger.warning(
                    f"⚠️ Could not update generation progress (table may not exist): {str(e)}"
                )

            # Save images to database
            saved_images = []
            for i, result in enumerate(image_results):
                if result.get("success") and result.get("images"):
                    for img in result["images"]:
                        # Download image from Fal URL and upload to S3
                        fal_url = img["url"]
                        try:
                            s3_url, s3_key, image_id = save_image_from_url(fal_url, scene_id)
                            image_asset = ImageAsset(
                                asset_id=image_id,  # Use image_id as asset_id
                                org_id=org_id,
                                s3_url=s3_url,  # Save S3 URL
                                local_path=None,
                                generation_id=generation_id,
                                scene_id=scene_id,
                                script_id=scene.script_id,
                                is_selected=False,
                                status="active",
                                created_at=datetime.utcnow(),
                            )
                            db.add(image_asset)
                            saved_images.append(image_asset)
                            logger.info(f"✅ Downloaded and uploaded image to S3: {s3_url}")
                        except Exception as upload_error:
                            logger.error(f"❌ Failed to upload image to S3: {str(upload_error)}")
                            # Fallback: save Fal URL if S3 upload fails
                            image_id = str(uuid.uuid4())
                            image_asset = ImageAsset(
                                asset_id=image_id,
                                org_id=org_id,
                                s3_url=fal_url,  # Fallback to Fal URL
                                local_path=None,
                                generation_id=generation_id,
                                scene_id=scene_id,
                                script_id=scene.script_id,
                                is_selected=False,
                                status="active",
                                created_at=datetime.utcnow(),
                            )
                            db.add(image_asset)
                            saved_images.append(image_asset)
                            logger.warning(f"⚠️ Saved Fal URL as fallback: {fal_url}")

            # Update generation status
            try:
                if generation:
                    generation.status = "completed"
                    generation.progress = 100
                    generation.completed_at = datetime.utcnow()
                    generation.result = {
                        "images_generated": len(saved_images),
                        "successful_generations": len(image_results),
                        "aspect_ratio": aspect_ratio,
                        "include_brand": include_brand,
                    }

                    db.commit()
            except Exception as e:
                logger.warning(
                    f"⚠️ Could not update generation status (table may not exist): {str(e)}"
                )

            logger.success(
                f"✅ Image generation completed for {generation_id}: {len(saved_images)} images"
            )

        finally:
            db.close()

    except Exception as e:
        logger.error(f"❌ Background image generation failed for {generation_id}: {str(e)}")

        # Update generation status to failed
        try:
            db = next(get_sync_db())
            try:
                generation = (
                    db.query(ImageGeneration).filter(ImageGeneration.id == generation_id).first()
                )

                if generation:
                    generation.status = "failed"
                    generation.error_message = str(e)
                    generation.completed_at = datetime.utcnow()
                    db.commit()
            except Exception as db_error:
                logger.warning(
                    f"⚠️ Could not update generation error status (table may not exist): {str(db_error)}"
                )

            db.close()
        except:
            pass


def _generate_images_with_custom_prompt_background(
    generation_id: str,
    scene_id: str,
    custom_prompt: str,
    aspect_ratio: str,
    include_brand: bool,
    org_id: str,
    user_id: str,
    previous_generation_ids: list = None,
):
    """
    Background task to generate images using a custom prompt
    """
    try:
        logger.info(f"🎨 Starting image generation with custom prompt for scene {scene_id}")

        # Initialize the image generation service
        image_service = ImageGenerationService()
        s3_client = S3Client(os.getenv("AWS_DEFAULT_REGION", "us-east-2"))

        db = next(get_sync_db())

        try:
            # Get the scene
            scene = db.query(Scene).filter(Scene.id == scene_id).first()

            if not scene:
                logger.error(f"❌ Scene {scene_id} not found")
                return

            # Update generation status to processing
            generation = (
                db.query(ImageGeneration).filter(ImageGeneration.id == generation_id).first()
            )

            if generation:
                generation.status = "processing"
                generation.started_at = datetime.utcnow()
                db.commit()

            logger.info(f"�️ Deleting previous images for scene {scene_id}...")

            # Delete previous images from S3 and mark as inactive in database
            try:
                deleted_count = delete_previous_images_for_scene(scene_id, org_id, db)
                logger.info(f"✅ Deleted {deleted_count} previous images for scene {scene_id}")
            except Exception as delete_error:
                logger.warning(f"⚠️ Error deleting previous images: {str(delete_error)}")
                # Continue with generation even if deletion fails

            logger.info(f"�🔧 Generating images with custom prompt: {custom_prompt[:100]}...")

            # Generate images using the custom prompt directly
            generation_result = image_service.generate_single_image(
                prompt=custom_prompt,
                scene_data=None,  # We're using the custom prompt directly
                aspect_ratio=aspect_ratio,
                num_images=1,  # Generate 1 image to replace the existing one
                include_brand=include_brand,
            )

            if not generation_result.get("success"):
                raise Exception(f"Image generation failed: {generation_result.get('error')}")

            image_results = generation_result.get("images", [])
            logger.info(f"✅ Generated {len(image_results)} images for scene {scene_id}")

            # Save images to S3 and create database records
            saved_images = []

            for i, image_data in enumerate(image_results):
                try:
                    # Extract image URL from the result
                    if isinstance(image_data, dict) and "url" in image_data:
                        image_url = image_data["url"]
                    elif isinstance(image_data, str):
                        image_url = image_data
                    else:
                        logger.error(f"❌ Invalid image data format: {type(image_data)}")
                        continue

                    # Use the existing save_image_from_url function
                    s3_url, s3_key, image_id = save_image_from_url(image_url, scene_id)

                    # Create ImageAsset record
                    image_asset = ImageAsset(
                        asset_id=image_id,
                        org_id=org_id,
                        generation_id=generation_id,
                        s3_url=s3_url,
                        scene_id=UUID(scene_id),
                        is_selected=False,
                        status="active",
                        created_at=datetime.utcnow(),
                    )

                    db.add(image_asset)
                    saved_images.append(image_asset)

                    logger.info(f"💾 Saved image {i+1}/{len(image_results)} to S3: {s3_key}")

                except Exception as img_error:
                    logger.error(f"❌ Failed to save image {i}: {str(img_error)}")

            # Update generation status to completed
            if generation:
                generation.status = "completed"
                generation.completed_at = datetime.utcnow()
                generation.metadata = {
                    "custom_prompt_used": True,
                    "prompt": custom_prompt,
                    "successful_generations": len(saved_images),
                    "aspect_ratio": aspect_ratio,
                    "include_brand": include_brand,
                }

            # Now cleanup previous generation records (after new images are saved)
            if previous_generation_ids:
                logger.info(
                    f"🗑️ Cleaning up {len(previous_generation_ids)} previous generation records..."
                )

                # Delete previous ImageAsset records first (to avoid foreign key violations)
                for prev_gen_id in previous_generation_ids:
                    try:
                        # Delete ImageAsset records referencing this generation
                        prev_assets = (
                            db.query(ImageAsset)
                            .filter(ImageAsset.generation_id == prev_gen_id)
                            .all()
                        )

                        for asset in prev_assets:
                            db.delete(asset)
                            logger.info(f"🗑️ Deleted ImageAsset record: {asset.asset_id}")

                        # Now delete the ImageGeneration record
                        prev_generation = (
                            db.query(ImageGeneration)
                            .filter(ImageGeneration.id == prev_gen_id)
                            .first()
                        )

                        if prev_generation:
                            db.delete(prev_generation)
                            logger.info(f"🗑️ Deleted ImageGeneration record: {prev_gen_id}")

                    except Exception as cleanup_error:
                        logger.warning(
                            f"⚠️ Error cleaning up generation {prev_gen_id}: {str(cleanup_error)}"
                        )

                logger.info(f"✅ Completed cleanup of previous generation records")

            db.commit()

            logger.success(
                f"✅ Custom prompt image generation completed for {generation_id}: {len(saved_images)} images"
            )

        finally:
            db.close()

    except Exception as e:
        logger.error(
            f"❌ Background custom prompt image generation failed for {generation_id}: {str(e)}"
        )

        # Update generation status to failed
        try:
            db = next(get_sync_db())
            try:
                generation = (
                    db.query(ImageGeneration).filter(ImageGeneration.id == generation_id).first()
                )

                if generation:
                    generation.status = "failed"
                    generation.error_message = str(e)
                    generation.completed_at = datetime.utcnow()
                    db.commit()
            except Exception as db_error:
                logger.warning(f"⚠️ Could not update generation error status: {str(db_error)}")

            db.close()
        except:
            pass
