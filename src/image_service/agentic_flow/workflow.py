"""
LangGraph workflow for the agentic image flow
"""

from typing import Dict, List, Any, Optional
from langgraph.graph import StateGraph, END
from loguru import logger

from .state import Agentic<PERSON>mageState, create_initial_state, update_type_distribution
from .agents import FlowDeciderAgent, CharacterProfileAgent, ImagePromptAgent


class AgenticImageWorkflow:
    """Main workflow orchestrator for agentic image generation"""

    def __init__(self):
        self.flow_decider = FlowDeciderAgent()
        self.character_agent = CharacterProfileAgent()
        self.prompt_agent = ImagePromptAgent()

        # Build the workflow graph
        self.workflow = self._build_workflow()

    def _build_workflow(self) -> StateGraph:
        """Build the LangGraph workflow"""

        # Create the state graph
        workflow = StateGraph(AgenticImageState)

        # Add nodes
        workflow.add_node("extract_character_profile", self._extract_character_profile)
        workflow.add_node("decide_scene_plan", self._decide_scene_plan)
        workflow.add_node("generate_prompt", self._generate_prompt)
        workflow.add_node("check_completion", self._check_completion)

        # Define the flow
        workflow.set_entry_point("extract_character_profile")

        workflow.add_edge("extract_character_profile", "decide_scene_plan")
        workflow.add_edge("decide_scene_plan", "generate_prompt")
        workflow.add_edge("generate_prompt", "check_completion")

        # Conditional edge from check_completion
        workflow.add_conditional_edges(
            "check_completion", self._should_continue, {"continue": "decide_scene_plan", "end": END}
        )

        return workflow.compile()

    def _extract_character_profile(self, state: AgenticImageState) -> AgenticImageState:
        """Extract character profile from all scenes"""
        logger.info("🎭 Extracting character profile...")

        try:
            character_profile = self.character_agent.extract_character_profile(state)
            state["character_profile"] = character_profile
            logger.success("✅ Character profile extracted")

        except Exception as e:
            logger.error(f"❌ Error extracting character profile: {e}")
            state["errors"].append(f"Character profile extraction failed: {str(e)}")
            state["character_profile"] = "Professional adult character with authentic appearance"

        return state

    def _decide_scene_plan(self, state: AgenticImageState) -> AgenticImageState:
        """Decide the plan for the current scene"""
        scene_index = state["current_scene_index"]
        scene_number = scene_index + 1

        logger.info(f"🎯 Deciding plan for scene {scene_number}/{state['total_scenes']}...")

        try:
            plan = self.flow_decider.decide_scene_plan(state, scene_index)
            state["scene_plans"].append(plan)

            # Update type distribution
            update_type_distribution(state, plan.image_type)

            logger.success(f"✅ Plan decided for scene {scene_number}: {plan.image_type.value}")
            logger.info(f"   Rationale: {plan.rationale}")

        except Exception as e:
            logger.error(f"❌ Error deciding scene plan: {e}")
            state["errors"].append(f"Scene plan decision failed for scene {scene_number}: {str(e)}")

        return state

    def _generate_prompt(self, state: AgenticImageState) -> AgenticImageState:
        """Generate image prompt for the current scene"""
        scene_index = state["current_scene_index"]
        scene_number = scene_index + 1

        logger.info(f"✍️ Generating prompt for scene {scene_number}...")

        try:
            # Get the plan for this scene
            plan = state["scene_plans"][scene_index]

            # Generate the prompt
            prompt = self.prompt_agent.generate_prompt(state, scene_index, plan)

            # Create the output record
            scene = state["scenes"][scene_index]
            prompt_record = {
                "scene_number": plan.scene_number,
                "scene_id": scene.get("id"),
                "scene_description": scene.get("description", ""),
                "image_type": plan.image_type.value,
                "aspect_ratio": plan.aspect_ratio,
                "style_preset": plan.style_preset.value,
                "include_brand": plan.include_brand,
                "weight_score": plan.weight_score,
                "rationale": plan.rationale,
                "image_prompt": prompt,
            }

            state["image_prompts"].append(prompt_record)

            logger.success(f"✅ Prompt generated for scene {scene_number}")
            logger.debug(f"   Prompt preview: {prompt[:100]}...")

        except Exception as e:
            logger.error(f"❌ Error generating prompt: {e}")
            state["errors"].append(f"Prompt generation failed for scene {scene_number}: {str(e)}")

        return state

    def _check_completion(self, state: AgenticImageState) -> AgenticImageState:
        """Check if all scenes have been processed"""
        state["current_scene_index"] += 1

        if state["current_scene_index"] >= state["total_scenes"]:
            state["processing_complete"] = True
            logger.success(f"🎉 All {state['total_scenes']} scenes processed!")

            # Log final distribution
            total = sum(state["current_type_distribution"].values())
            logger.info("📊 Final type distribution:")
            for type_name, count in state["current_type_distribution"].items():
                percentage = (count / total * 100) if total > 0 else 0
                logger.info(f"   {type_name.title()}: {count}/{total} ({percentage:.1f}%)")

        return state

    def _should_continue(self, state: AgenticImageState) -> str:
        """Determine if the workflow should continue"""
        if state["processing_complete"]:
            return "end"
        else:
            return "continue"

    def process_scenes(
        self,
        scenes: List[Dict[str, Any]],
        brand_info: Optional[Dict[str, Any]] = None,
        global_style: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Process scenes through the agentic image flow

        Args:
            scenes: List of scene dictionaries
            brand_info: Optional brand information
            global_style: Optional global style preferences

        Returns:
            Dictionary with processed results
        """
        logger.info(f"🚀 Starting agentic image flow for {len(scenes)} scenes...")

        # Create initial state
        initial_state = create_initial_state(scenes, brand_info, global_style)

        try:
            # Run the workflow
            final_state = self.workflow.invoke(initial_state)

            # Prepare results
            results = {
                "success": True,
                "total_scenes": final_state["total_scenes"],
                "character_profile": final_state["character_profile"],
                "scene_plans": [
                    {
                        "scene_number": plan.scene_number,
                        "image_type": plan.image_type.value,
                        "aspect_ratio": plan.aspect_ratio,
                        "style_preset": plan.style_preset.value,
                        "include_brand": plan.include_brand,
                        "weight_score": plan.weight_score,
                        "rationale": plan.rationale,
                    }
                    for plan in final_state["scene_plans"]
                ],
                "image_prompts": final_state["image_prompts"],
                "type_distribution": final_state["current_type_distribution"],
                "errors": final_state["errors"],
            }

            logger.success(f"✅ Agentic image flow completed successfully!")

            return results

        except Exception as e:
            logger.error(f"❌ Agentic image flow failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "total_scenes": len(scenes),
                "character_profile": "",
                "scene_plans": [],
                "image_prompts": [],
                "type_distribution": {},
                "errors": [str(e)],
            }
