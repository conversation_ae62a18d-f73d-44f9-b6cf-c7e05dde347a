"""
Main service for the agentic image flow
"""

from typing import Dict, List, Any, Optional
from loguru import logger

from .workflow import AgenticImageWorkflow


class AgenticImageFlowService:
    """
    Main service for agentic image generation flow

    This service orchestrates the entire image generation process using
    LangChain and LangGraph for structured, scalable agent interactions.
    """

    def __init__(self):
        self.workflow = AgenticImageWorkflow()

    def generate_scene_image_plans(
        self,
        scenes: List[Dict[str, Any]],
        brand_info: Optional[Dict[str, Any]] = None,
        global_style: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Generate comprehensive image plans for all scenes

        Args:
            scenes: List of scene dictionaries with description, visual, etc.
            brand_info: Optional brand information for integration
            global_style: Optional global style preferences

        Returns:
            Dictionary containing:
            - success: bool
            - scene_plans: List of scene plans with type, style, etc.
            - image_prompts: List of generated prompts
            - character_profile: Extracted character profile
            - type_distribution: Final distribution of image types
            - errors: List of any errors encountered
        """
        logger.info(f"🎨 Starting agentic image flow for {len(scenes)} scenes")

        if not scenes:
            return {
                "success": False,
                "error": "No scenes provided",
                "scene_plans": [],
                "image_prompts": [],
                "character_profile": "",
                "type_distribution": {},
                "errors": ["No scenes provided"],
            }

        try:
            # Process scenes through the workflow
            results = self.workflow.process_scenes(scenes, brand_info, global_style)

            logger.success(f"✅ Agentic image flow completed for {len(scenes)} scenes")

            return results

        except Exception as e:
            logger.error(f"❌ Agentic image flow service failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "scene_plans": [],
                "image_prompts": [],
                "character_profile": "",
                "type_distribution": {},
                "errors": [str(e)],
            }

    def get_scene_prompt(
        self,
        scene: Dict[str, Any],
        scene_plan: Dict[str, Any],
        character_profile: str = "",
        brand_info: Optional[Dict[str, Any]] = None,
    ) -> str:
        """
        Get a single scene prompt (for individual scene processing)

        Args:
            scene: Scene dictionary
            scene_plan: Scene plan from the flow decider
            character_profile: Character profile for consistency
            brand_info: Optional brand information

        Returns:
            Generated image prompt string
        """
        try:
            # Create a minimal workflow for single scene
            scenes = [scene]

            # Set up brand info if provided
            brand_data = None
            if brand_info:
                brand_data = {
                    "name": brand_info.get("name", ""),
                    "description": brand_info.get("description", ""),
                    "visual_style": brand_info.get("visual_style", ""),
                    "colors": brand_info.get("colors", []),
                }

            # Process through workflow
            results = self.workflow.process_scenes(scenes, brand_data)

            if results["success"] and results["image_prompts"]:
                return results["image_prompts"][0]["image_prompt"]
            else:
                logger.warning("Failed to generate prompt, using fallback")
                return f"Professional {scene_plan.get('image_type', 'character')} scene: {scene.get('description', 'scene')}"

        except Exception as e:
            logger.error(f"❌ Error getting scene prompt: {e}")
            return f"Professional scene: {scene.get('description', 'scene')}"

    def analyze_scene_distribution(self, scenes: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Analyze optimal distribution of image types for given scenes

        Args:
            scenes: List of scene dictionaries

        Returns:
            Dictionary with distribution analysis
        """
        try:
            # Run just the flow decider part
            results = self.workflow.process_scenes(scenes)

            if results["success"]:
                total_scenes = len(scenes)
                distribution = results["type_distribution"]

                analysis = {
                    "total_scenes": total_scenes,
                    "recommended_distribution": distribution,
                    "percentages": {
                        type_name: (count / total_scenes * 100) if total_scenes > 0 else 0
                        for type_name, count in distribution.items()
                    },
                    "balance_score": self._calculate_balance_score(distribution, total_scenes),
                }

                return analysis
            else:
                return {"error": "Failed to analyze distribution"}

        except Exception as e:
            logger.error(f"❌ Error analyzing scene distribution: {e}")
            return {"error": str(e)}

    def _calculate_balance_score(self, distribution: Dict[str, int], total: int) -> float:
        """Calculate how well balanced the distribution is"""
        if total == 0:
            return 0.0

        target_weights = {
            "object": 0.3,
            "character": 0.4,
            "object_character": 0.25,
            "montage": 0.05,
        }

        score = 0.0
        for type_name, target_weight in target_weights.items():
            actual_ratio = distribution.get(type_name, 0) / total
            # Score is higher when closer to target
            deviation = abs(actual_ratio - target_weight)
            score += max(0, 1.0 - deviation * 2)  # Penalty for deviation

        return score / len(target_weights)  # Average score

    def get_workflow_status(self) -> Dict[str, Any]:
        """Get status information about the workflow"""
        return {
            "workflow_available": True,
            "agents": {
                "flow_decider": "available",
                "character_profile": "available",
                "image_prompt": "available",
            },
            "supported_image_types": ["object", "character", "object_character", "montage"],
            "supported_styles": [
                "cinematic",
                "commercial",
                "lifestyle",
                "product_focus",
                "artistic",
            ],
            "supported_ratios": ["16:9", "9:16", "1:1"],
        }
