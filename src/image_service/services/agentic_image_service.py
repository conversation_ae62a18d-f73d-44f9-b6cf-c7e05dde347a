"""
Agentic Image Service
Enhanced service using LangChain and LangGraph for structured agent orchestration.
"""

# Global imports
from typing import Any, Dict, List, Optional
from loguru import logger

# Local imports
from ..agentic_flow import AgenticImageFlowService


class AgenticImageService:
    """
    High-level service for agentic image planning and prompt generation.

    Powered by LangChain and LangGraph for scalable, structured agent workflows.
    """

    def __init__(self):
        # LangGraph-based flow
        self.agentic_flow = AgenticImageFlowService()
        self.brand_info: Dict[str, Any] = {}
        self.global_style: Dict[str, Any] = {}

    def set_brand_info(self, brand_info: Dict[str, Any]):
        """Set brand information for image generation"""
        self.brand_info = brand_info or {}

    def set_global_style(self, style: Dict[str, Any]):
        """Set global style preferences"""
        self.global_style = style or {}

    def plan_and_generate_prompts(self, scenes: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Generate comprehensive plans and prompts for all scenes using LangGraph agentic flow.

        Returns enriched scene records with type, params, and generated prompt.
        """
        if not scenes:
            return []

        logger.info(f"🎯 Starting LangGraph agentic flow for {len(scenes)} scenes")

        try:
            # Process scenes through the agentic workflow
            results = self.agentic_flow.generate_scene_image_plans(
                scenes=scenes, brand_info=self.brand_info, global_style=self.global_style
            )

            if not results["success"]:
                logger.error(f"❌ LangGraph flow failed: {results.get('error')}")
                return self._generate_fallback_prompts(scenes)

            # Convert results to expected format
            outputs = []
            for prompt_data in results["image_prompts"]:
                outputs.append(
                    {
                        "scene_number": prompt_data["scene_number"],
                        "scene_description": prompt_data["scene_description"],
                        "original_visual": prompt_data.get("original_visual", ""),
                        "duration": "",  # Not included in prompt data
                        "type": prompt_data["image_type"],
                        "aspect_ratio": prompt_data["aspect_ratio"],
                        "style_preset": prompt_data["style_preset"],
                        "includes_brand": prompt_data["include_brand"],
                        "image_prompt": prompt_data["image_prompt"],
                        "rationale": prompt_data["rationale"],
                        "weight_score": prompt_data["weight_score"],
                    }
                )

            logger.success(f"✅ Generated {len(outputs)} scene prompts using LangGraph")
            logger.info(f"📊 Type distribution: {results['type_distribution']}")

            return outputs

        except Exception as e:
            logger.error(f"❌ Error in LangGraph flow: {e}")
            return self._generate_fallback_prompts(scenes)

    def _generate_fallback_prompts(self, scenes: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Generate simple fallback prompts if agentic flow fails"""
        logger.warning("🔄 Using fallback prompt generation")

        outputs = []
        for idx, scene in enumerate(scenes):
            scene_number = scene.get("number", idx + 1)
            description = scene.get("description", "")

            # Simple fallback prompt
            prompt = f"Professional cinematic scene: {description}"

            outputs.append(
                {
                    "scene_number": scene_number,
                    "scene_description": description,
                    "original_visual": scene.get("visual", ""),
                    "duration": scene.get("duration", ""),
                    "type": "character",
                    "aspect_ratio": "16:9",
                    "style_preset": "cinematic",
                    "includes_brand": False,
                    "image_prompt": prompt,
                    "rationale": "fallback generation",
                    "weight_score": 0.5,
                }
            )

        return outputs

    def get_workflow_status(self) -> Dict[str, Any]:
        """Get status of the agentic workflow"""
        return self.agentic_flow.get_workflow_status()

    def analyze_scene_distribution(self, scenes: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze optimal distribution of image types for scenes"""
        return self.agentic_flow.analyze_scene_distribution(scenes)
