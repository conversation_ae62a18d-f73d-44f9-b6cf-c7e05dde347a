"""
Batch Image Generation Service
Coordinates parallel image generation for multiple scenes
"""

from typing import Dict, List, Optional, Any, Callable
from loguru import logger

from .agentic_image_service import AgenticImageService
from .image_generator import ImageGenerationService


class BatchImageGenerationService:
    """
    Service for coordinating batch image generation with parallel processing
    """

    def __init__(self, max_concurrent_images: int = 3):
        """
        Initialize the batch image generation service
        
        Args:
            max_concurrent_images: Maximum number of images to generate concurrently
        """
        self.agentic_service = AgenticImageService()
        self.image_generator = ImageGenerationService()
        self.max_concurrent_images = max_concurrent_images
        
    def set_brand_info(self, brand_info: Dict[str, Any]):
        """Set brand information for both services"""
        self.agentic_service.set_brand_info(brand_info)
        self.image_generator.set_brand_info(brand_info)
        
    def set_global_style(self, style: Dict[str, Any]):
        """Set global style preferences"""
        self.agentic_service.set_global_style(style)

    async def generate_images_for_scenes(
        self,
        scenes_data: List[Dict[str, Any]],
        aspect_ratio: str = "16:9",
        include_brand: bool = True,
        progress_callback: Optional[Callable[[str, int, int, str], None]] = None,
    ) -> Dict[str, Any]:
        """
        Generate images for multiple scenes using batch agentic planning and parallel generation
        
        Args:
            scenes_data: List of scene dictionaries with scene information
            aspect_ratio: Aspect ratio for generated images
            include_brand: Whether to include brand integration
            progress_callback: Optional callback for progress updates (stage, current, total, message)
            
        Returns:
            Dictionary with generation results and metadata
        """
        total_scenes = len(scenes_data)
        logger.info(f"🚀 Starting batch image generation for {total_scenes} scenes")
        
        try:
            # Stage 1: Batch Agentic Planning
            if progress_callback:
                progress_callback("planning", 0, total_scenes, "Starting agentic planning for all scenes")
            
            logger.info("🧠 Running batch agentic planning...")
            scene_prompts = self.agentic_service.plan_and_generate_prompts(scenes_data)
            
            if not scene_prompts:
                raise Exception("Agentic planning failed to generate prompts")
            
            logger.success(f"✅ Agentic planning completed for {len(scene_prompts)} scenes")
            
            if progress_callback:
                progress_callback("planning", total_scenes, total_scenes, "Agentic planning completed")
            
            # Stage 2: Parallel Image Generation
            if progress_callback:
                progress_callback("generation", 0, total_scenes, "Starting parallel image generation")
            
            def image_progress_callback(current: int, total: int, scene_id: str):
                if progress_callback:
                    progress_callback("generation", current + 1, total, f"Generating image for scene {scene_id}")
            
            logger.info(f"🎨 Starting parallel image generation (max_concurrent={self.max_concurrent_images})")
            generation_results = await self.image_generator.generate_images_parallel(
                scene_prompts=scene_prompts,
                max_concurrent=self.max_concurrent_images,
                progress_callback=image_progress_callback
            )
            
            # Stage 3: Process Results
            if progress_callback:
                progress_callback("processing", 0, total_scenes, "Processing generation results")
            
            successful_generations = []
            failed_generations = []
            
            for i, result in enumerate(generation_results):
                scene_prompt = scene_prompts[i] if i < len(scene_prompts) else {}
                
                if result.get("success") and result.get("images"):
                    successful_generations.append({
                        "scene_id": result.get("scene_id"),
                        "scene_number": result.get("scene_number"),
                        "images": result["images"],
                        "prompt": scene_prompt.get("image_prompt", ""),
                        "metadata": {
                            "image_type": scene_prompt.get("type"),
                            "aspect_ratio": scene_prompt.get("aspect_ratio"),
                            "style_preset": scene_prompt.get("style_preset"),
                            "includes_brand": scene_prompt.get("includes_brand"),
                            "rationale": scene_prompt.get("rationale"),
                            "weight_score": scene_prompt.get("weight_score"),
                        }
                    })
                else:
                    failed_generations.append({
                        "scene_id": result.get("scene_id"),
                        "scene_number": result.get("scene_number"),
                        "error": result.get("error", "Unknown error"),
                        "prompt": scene_prompt.get("image_prompt", ""),
                    })
            
            if progress_callback:
                progress_callback("processing", total_scenes, total_scenes, "Batch generation completed")
            
            # Prepare final results
            results = {
                "success": True,
                "total_scenes": total_scenes,
                "successful_generations": len(successful_generations),
                "failed_generations": len(failed_generations),
                "success_rate": f"{(len(successful_generations) / total_scenes * 100):.1f}%",
                "successful_results": successful_generations,
                "failed_results": failed_generations,
                "scene_prompts": scene_prompts,
                "generation_metadata": {
                    "max_concurrent_images": self.max_concurrent_images,
                    "total_processing_time": None,  # Could add timing if needed
                    "agentic_planning_enabled": True,
                }
            }
            
            logger.success(
                f"🎉 Batch generation completed: {len(successful_generations)}/{total_scenes} successful "
                f"({results['success_rate']})"
            )
            
            return results
            
        except Exception as e:
            logger.error(f"❌ Batch image generation failed: {e}")
            if progress_callback:
                progress_callback("error", 0, total_scenes, f"Batch generation failed: {str(e)}")
            
            return {
                "success": False,
                "error": str(e),
                "total_scenes": total_scenes,
                "successful_generations": 0,
                "failed_generations": total_scenes,
                "success_rate": "0%",
                "successful_results": [],
                "failed_results": [],
                "scene_prompts": [],
                "generation_metadata": {
                    "max_concurrent_images": self.max_concurrent_images,
                    "agentic_planning_enabled": True,
                    "error": str(e),
                }
            }

    def update_concurrency_limit(self, max_concurrent: int):
        """Update the maximum concurrent image generation limit"""
        self.max_concurrent_images = max_concurrent
        logger.info(f"🔧 Updated max concurrent images to {max_concurrent}")

    def get_service_status(self) -> Dict[str, Any]:
        """Get the current status of the batch service"""
        return {
            "max_concurrent_images": self.max_concurrent_images,
            "agentic_service_status": self.agentic_service.get_workflow_status(),
            "image_generator_initialized": hasattr(self.image_generator, 'flux_generator'),
        }
