"""
Script generation service with Celery task integration
Handles script generation, refinement, and status tracking using enhanced professional advertisement framework
"""

# Global imports
import uuid
import asyncio
from uuid import UUID
from loguru import logger
from datetime import datetime
from sqlalchemy import select, update
from typing import Optional, Dict, Any, List
from sqlalchemy.ext.asyncio import AsyncSession

# Local imports
from src.worker.celery_app import celery_app
from src.shared.config.database import get_db, get_sync_db
from src.script_service.services.script_generator import script_generator
from src.script_service.utils.generators.scene_processor import scene_processor
from src.script_service.services.scene_service import process_scenes_for_script
from src.shared.models.database_models import Script, TaskQueue, TextAsset, Scene


@celery_app.task(name="script_service.generate_script")
def generate_script_task(script_id: str, script_data: Dict[str, Any]):
    """
    Celery task for script generation using enhanced professional advertisement framework
    """
    # VERY VERBOSE LOGGING - ADDED FOR DEBUGGING
    print("=" * 80)
    logger.info(" ENTERING generate_script_task")
    logger.info(f" script_id = {script_id}")
    logger.info(f" script_data = {script_data}")
    logger.info(f" task_id = {generate_script_task.request.id}")
    logger.info(f" task_name = {generate_script_task.request.task}")
    print("=" * 80)

    # Get the task_id from the current task request
    task_id = generate_script_task.request.id
    logger.info(f"Starting enhanced script generation task {task_id} for script {script_id}")

    logger.info(" About to get database session...")
    db = next(get_sync_db())
    logger.info(" Database session obtained successfully")

    try:
        logger.info(" Looking up Script in DB...")
        logger.info(f"Looking up Script {script_id} in DB...")
        script_obj = db.query(Script).filter(Script.id == script_id).first()
        if not script_obj:
            logger.info(f" Script {script_id} not found in DB!")
            logger.error(f"Script {script_id} not found in DB!")
            raise Exception(f"Script {script_id} not found in DB!")

        logger.info(f" Script found: {script_obj.id}")

        # Update task status to processing
        logger.info(" Looking up TaskQueue...")
        task_queue = db.query(TaskQueue).filter(TaskQueue.task_id == task_id).first()
        if task_queue:
            logger.info(" TaskQueue found, updating status...")
            task_queue.status = "processing"
            task_queue.progress = 10
            task_queue.started_at = datetime.utcnow()

        script_obj.generation_status = "processing"
        logger.info(" Committing initial status updates...")
        db.commit()
        logger.info(" Initial status updates committed successfully")

        # Generate script content using enhanced professional advertisement framework
        logger.info(" About to generate script content with enhanced framework...")
        logger.info(f"Generating enhanced professional script content for {script_id}")
        try:
            logger.info(" Calling script_generator.generate_initial_script...")
            script_content = script_generator.generate_initial_script(
                text_input=script_data["text_input"],
                video_style=script_data["video_style"],
                duration=script_data["duration"],
                brand_name=script_data.get("brand_name"),
                product_name=script_data.get("product_name"),
                brand_description=script_data.get("brand_description"),
            )

            logger.info(f" Enhanced script content generated, length: {len(script_content)}")

            if not script_content or len(script_content.strip()) == 0:
                logger.info(" Generated script content is empty!")
                raise Exception("Generated script content is empty")

            logger.info(
                f"Enhanced professional script content generated successfully, length: {len(script_content)}"
            )

        except Exception as e:
            logger.info(f" Enhanced script generation failed: {e}")
            logger.error(f"Enhanced script generation failed: {e}")
            raise Exception(f"Enhanced script generation failed: {str(e)}")

        # Save script content to text asset
        logger.info(" Creating text asset...")
        asset_id = f"script-{uuid.uuid4().hex[:12]}"
        text_asset = TextAsset(
            org_id=script_obj.org_id,
            asset_id=asset_id,
            content=script_content,
            type="script",
            metadata={"script_id": script_id, "generated_at": datetime.utcnow().isoformat()},
        )
        db.add(text_asset)
        logger.info(" Flushing text asset...")
        db.flush()

        script_obj.text_asset_id = asset_id
        # Update script with narration_script in extra_metadata
        meta = dict(script_obj.extra_metadata) if script_obj.extra_metadata else {}
        meta["narration_script"] = script_content
        script_obj.extra_metadata = meta

        if task_queue:
            task_queue.progress = 50

        logger.info(" Committing script content...")
        db.commit()
        logger.info(" Script content committed successfully")

        # Extract and process scenes synchronously with better error handling
        logger.info(" About to extract scenes...")
        logger.info(f"Extracting scenes for script {script_id}")
        try:
            scenes = process_scenes_for_script_sync(script_id, script_content)
            logger.info(f" Scene creation complete, scenes count: {len(scenes)}")
            logger.info(f"Scene creation complete for script {script_id}. Scenes: {len(scenes)}")
        except Exception as e:
            logger.info(f" Scene processing failed: {e}")
            logger.error(f"Scene processing failed: {e}")
            # Don't fail the entire task if scene processing fails
            scenes = []

        # Update status to completed
        logger.info(" Updating final status...")
        if task_queue:
            task_queue.status = "completed"
            task_queue.progress = 100
            task_queue.completed_at = datetime.utcnow()
            task_queue.result = {"scenes_count": len(scenes)}

        script_obj.generation_status = "completed"
        script_obj.completed_at = datetime.utcnow()
        logger.info(" Committing final status...")
        db.commit()
        logger.info(" Final status committed successfully")

        result = {
            "script_id": script_id,
            "text_asset_id": asset_id,
            "scenes_count": len(scenes),
            "status": "completed",
        }
        logger.info(f" Task completed successfully with result: {result}")
        logger.success(
            f"Enhanced script generation task {task_id} completed successfully with {len(scenes)} scenes"
        )
        return result

    except Exception as e:
        logger.info(f" Task failed with exception: {e}")
        db.rollback()
        logger.error(f"Task {task_id} failed: {str(e)}")

        # Update task status to failed
        task_queue = db.query(TaskQueue).filter(TaskQueue.task_id == task_id).first()
        if task_queue:
            task_queue.status = "failed"
            task_queue.progress = 0
            task_queue.error_message = str(e)
            task_queue.completed_at = datetime.utcnow()

        # Update script status to failed
        script_obj = db.query(Script).filter(Script.id == script_id).first()
        if script_obj:
            script_obj.generation_status = "failed"
            script_obj.error_message = str(e)

        db.commit()
        raise


@celery_app.task(name="script_service.regenerate_script")
def regenerate_script_task(script_id: str, feedback: str):
    """
    Celery task for script regeneration using enhanced professional advertisement framework
    """
    task_id = str(uuid.uuid4())
    logger.info(f"Starting enhanced script regeneration task {task_id} for script {script_id}")

    try:
        db = next(get_sync_db())
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(update_task_status(task_id, "processing", 10))
        current_script = loop.run_until_complete(get_script_content_from_text_asset(script_id))
        if not current_script:
            raise ValueError("Script not found or has no content")

        loop.run_until_complete(update_task_status(task_id, "processing", 30))

        # Use enhanced script refinement
        refined_script = script_generator.refine_script(current_script, feedback)

        loop.run_until_complete(update_task_status(task_id, "processing", 70))
        text_asset_id = loop.run_until_complete(
            save_script_content_to_text_asset(script_id, refined_script)
        )
        scenes = loop.run_until_complete(process_scenes_for_script(script_id, refined_script))

        loop.run_until_complete(
            update_task_status(task_id, "completed", 100, {"scenes_count": len(scenes)})
        )
        loop.run_until_complete(update_script_status(script_id, "completed"))

        return {
            "script_id": script_id,
            "text_asset_id": text_asset_id,
            "scenes_count": len(scenes),
            "status": "completed",
        }

    except Exception as e:
        logger.error(f"Task {task_id} failed: {str(e)}")
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(update_task_status(task_id, "failed", 0, error_message=str(e)))
        loop.run_until_complete(update_script_status(script_id, "failed", str(e)))
        raise


async def update_task_status(
    task_id: str,
    status: str,
    progress: int,
    result: Optional[Dict] = None,
    error_message: Optional[str] = None,
):
    """Update task status in database"""
    async for session in get_db():
        try:
            stmt = select(TaskQueue).where(TaskQueue.task_id == task_id)
            result_query = await session.execute(stmt)
            task = result_query.scalar_one_or_none()

            if task:
                task.status = status
                task.progress = progress
                if result:
                    task.result = result
                if error_message:
                    task.error_message = error_message
                if status == "completed":
                    task.completed_at = datetime.utcnow()
                elif status == "processing" and not task.started_at:
                    task.started_at = datetime.utcnow()

                await session.commit()
        except Exception as e:
            logger.error(f"Failed to update task status: {e}")
            await session.rollback()


async def update_script_status(script_id: str, status: str, error_message: Optional[str] = None):
    """Update script status in database"""
    async for session in get_db():
        try:
            stmt = select(Script).where(Script.id == script_id)
            result = await session.execute(stmt)
            script = result.scalar_one_or_none()

            if script:
                script.generation_status = status
                if error_message:
                    script.error_message = error_message
                if status == "completed":
                    script.completed_at = datetime.utcnow()

                await session.commit()
        except Exception as e:
            logger.error(f"Failed to update script status: {e}")
            await session.rollback()


async def save_script_content_to_text_asset(script_id: str, content: str) -> str:
    """Save script content to text asset and update script reference"""
    async for session in get_db():
        try:
            # Get script to get org_id
            stmt = select(Script).where(Script.id == script_id)
            result = await session.execute(stmt)
            script = result.scalar_one_or_none()

            if not script:
                raise ValueError("Script not found")

            # Create new text asset
            asset_id = f"script-{uuid.uuid4().hex[:12]}"
            text_asset = TextAsset(
                org_id=script.org_id,
                asset_id=asset_id,
                content=content,
                type="script",
                metadata={"script_id": script_id, "generated_at": datetime.utcnow().isoformat()},
            )
            session.add(text_asset)
            await session.flush()

            # Update script reference
            script.text_asset_id = asset_id
            meta = dict(script.extra_metadata) if script.extra_metadata else {}
            meta["narration_script"] = content
            script.extra_metadata = meta

            await session.commit()
            return asset_id

        except Exception as e:
            logger.error(f"Failed to save script content: {e}")
            await session.rollback()
            raise


async def get_script_content_from_text_asset(script_id: str) -> Optional[str]:
    """Get script content from text asset"""
    async for session in get_db():
        try:
            stmt = select(Script).where(Script.id == script_id)
            result = await session.execute(stmt)
            script = result.scalar_one_or_none()

            if not script or not script.text_asset_id:
                return None

            text_asset_stmt = select(TextAsset).where(TextAsset.asset_id == script.text_asset_id)
            text_asset_result = await session.execute(text_asset_stmt)
            text_asset = text_asset_result.scalar_one_or_none()

            return text_asset.content if text_asset else None

        except Exception as e:
            logger.error(f"Failed to get script content: {e}")
            return None


def update_script_narration(script_id: str, narration_script: str):
    """Update script narration in database"""

    async def _update():
        async for session in get_db():
            try:
                stmt = select(Script).where(Script.id == script_id)
                result = await session.execute(stmt)
                script = result.scalar_one_or_none()

                if script:
                    meta = dict(script.extra_metadata) if script.extra_metadata else {}
                    meta["narration_script"] = narration_script
                    script.extra_metadata = meta
                    await session.commit()

            except Exception as e:
                logger.error(f"Failed to update script narration: {e}")
                await session.rollback()

    asyncio.create_task(_update())


def process_scenes_for_script_sync(script_id: str, script_content: str) -> List[Dict]:
    """Process scenes from script content synchronously"""
    try:
        logger.info(f"Processing scenes for script {script_id}")
        scenes = scene_processor.process_script_content(script_content)

        # Delete existing scenes
        delete_existing_scenes_sync(script_id)

        # Save new scenes
        saved_scenes = save_scenes_to_database_sync(script_id, scenes)

        logger.success(f"Successfully processed {len(saved_scenes)} scenes for script {script_id}")
        return saved_scenes

    except Exception as e:
        logger.error(f"Failed to process scenes for script {script_id}: {e}")
        raise


def delete_existing_scenes_sync(script_id: str):
    """Delete existing scenes for a script synchronously"""
    db = next(get_sync_db())
    try:
        existing_scenes = db.query(Scene).filter(Scene.script_id == script_id).all()
        for scene in existing_scenes:
            db.delete(scene)
        db.commit()
        logger.info(f"Deleted {len(existing_scenes)} existing scenes for script {script_id}")
    except Exception as e:
        logger.error(f"Failed to delete existing scenes: {e}")
        db.rollback()
        raise


def save_scenes_to_database_sync(script_id: str, scenes_data: List[Dict]) -> List[Dict]:
    """Save scenes to database synchronously"""
    db = next(get_sync_db())
    try:
        saved_scenes = []
        for scene_data in scenes_data:
            scene = Scene(
                script_id=script_id,
                scene_number=scene_data.get("scene_number", 1),
                title=scene_data.get("title", ""),
                description=scene_data.get("description", ""),
                visual_description=scene_data.get("visual_description", ""),
                narration=scene_data.get("narration", ""),
                duration=scene_data.get("duration", "5s"),
                location_group=scene_data.get("location_group", 1),
                status="active",
                generation_status="completed",
                character_info=scene_data.get("character_info", {}),
                metadata=scene_data.get("metadata", {}),
            )
            db.add(scene)
            saved_scenes.append(scene_data)

        db.commit()
        logger.info(f"Saved {len(saved_scenes)} scenes to database for script {script_id}")
        return saved_scenes

    except Exception as e:
        logger.error(f"Failed to save scenes to database: {e}")
        db.rollback()
        raise
