"""
Scene processor for enhanced professional advertisement script format
Parses the professional script format and extracts scenes with proper structure
"""

# Global imports
import re
from loguru import logger
from typing import List, Dict, Any, Optional


class SceneProcessor:
    """Processes enhanced professional advertisement scripts to extract scenes"""

    def __init__(self):
        """Initialize the scene processor"""
        self.scene_pattern = re.compile(
            r"Scene\s+(\d+):\s*([^\n]+)\s*\n"
            r"Location Group:\s*([^\n]+)\s*\n"
            r"Visual:\s*([^\n]+(?:\n(?!Narration:)[^\n]+)*)\s*\n"
            r'Narration:\s*\(\(([^)]+)\)\)\s*"([^"]+)"\s*\n'
            r"SFX / Music:\s*([^\n]+)\s*\n"
            r"Transition:\s*([^\n]+)\s*\n"
            r"Duration:\s*([^\n]+)",
            re.MULTILINE | re.DOTALL,
        )

        self.character_profile_pattern = re.compile(
            r"\*\*CHARACTER PROFILE \(MAINTAIN CONSISTENCY\)\*\*\s*\n"
            r"Main Character:\s*([^\n]+(?:\n(?!Supporting Characters:)[^\n]+)*)",
            re.MULTILINE | re.DOTALL,
        )

        self.title_pattern = re.compile(r"Title:\s*([^\n]+)")
        self.overview_pattern = re.compile(r"Overview:\s*([^\n]+(?:\n[^\n]+)*)")

    def process_script_content(self, script_content: str) -> List[Dict[str, Any]]:
        """
        Process enhanced professional advertisement script content and extract scenes

        Args:
            script_content: The full script content in professional advertisement format

        Returns:
            List of scene dictionaries with proper structure
        """
        logger.info("Processing enhanced professional advertisement script content")

        try:
            # Extract character profile
            character_profile = self._extract_character_profile(script_content)

            # Extract title and overview
            title = self._extract_title(script_content)
            overview = self._extract_overview(script_content)

            # Extract scenes
            scenes = self._extract_scenes(script_content)

            # Enhance scenes with character profile and metadata
            enhanced_scenes = []
            for i, scene in enumerate(scenes):
                enhanced_scene = {
                    "scene_number": i + 1,
                    "title": scene.get("title", f"Scene {i + 1}"),
                    "description": scene.get("visual_description", ""),
                    "visual_description": scene.get("visual_description", ""),
                    "narration": scene.get("narration", ""),
                    "duration": scene.get("duration", "5s"),
                    "location_group": scene.get("location_group", 1),
                    "status": "active",
                    "generation_status": "completed",
                    "character_info": character_profile,
                    "metadata": {
                        "sfx_music": scene.get("sfx_music", ""),
                        "transition": scene.get("transition", ""),
                        "script_title": title,
                        "script_overview": overview,
                        "scene_type": "professional_advertisement",
                    },
                }
                enhanced_scenes.append(enhanced_scene)

            logger.success(
                f"Successfully processed {len(enhanced_scenes)} scenes from professional advertisement script"
            )
            return enhanced_scenes

        except Exception as e:
            logger.error(f"Failed to process script content: {e}")
            # Fallback to basic scene extraction
            return self._fallback_scene_extraction(script_content)

    def _extract_character_profile(self, script_content: str) -> Dict[str, Any]:
        """Extract character profile from script content"""
        try:
            match = self.character_profile_pattern.search(script_content)
            if match:
                character_text = match.group(1).strip()
                return {
                    "main_character": character_text,
                    "description": character_text,
                    "type": "professional_advertisement",
                }
        except Exception as e:
            logger.warning(f"Failed to extract character profile: {e}")

        return {
            "main_character": "Professional advertisement character",
            "description": "Professional advertisement character",
            "type": "professional_advertisement",
        }

    def _extract_title(self, script_content: str) -> str:
        """Extract title from script content"""
        try:
            match = self.title_pattern.search(script_content)
            if match:
                return match.group(1).strip()
        except Exception as e:
            logger.warning(f"Failed to extract title: {e}")

        return "Professional Advertisement"

    def _extract_overview(self, script_content: str) -> str:
        """Extract overview from script content"""
        try:
            match = self.overview_pattern.search(script_content)
            if match:
                return match.group(1).strip()
        except Exception as e:
            logger.warning(f"Failed to extract overview: {e}")

        return "Professional advertisement script"

    def _extract_scenes(self, script_content: str) -> List[Dict[str, Any]]:
        """Extract scenes from script content using regex pattern"""
        scenes = []

        try:
            matches = self.scene_pattern.finditer(script_content)

            for match in matches:
                scene_data = {
                    "scene_number": int(match.group(1)),
                    "title": match.group(2).strip(),
                    "location_group": self._parse_location_group(match.group(3)),
                    "visual_description": match.group(4).strip(),
                    "narration_type": match.group(5).strip(),
                    "narration": match.group(6).strip(),
                    "sfx_music": match.group(7).strip(),
                    "transition": match.group(8).strip(),
                    "duration": match.group(9).strip(),
                }
                scenes.append(scene_data)

            # Sort by scene number
            scenes.sort(key=lambda x: x["scene_number"])

        except Exception as e:
            logger.error(f"Failed to extract scenes with regex: {e}")
            # Fallback to basic extraction
            scenes = self._basic_scene_extraction(script_content)

        return scenes

    def _parse_location_group(self, location_text: str) -> int:
        """Parse location group from text"""
        try:
            # Extract number from "Location 1/2/3/4"
            match = re.search(r"Location\s+(\d+)", location_text)
            if match:
                return int(match.group(1))
        except Exception as e:
            logger.warning(f"Failed to parse location group: {e}")

        return 1

    def _basic_scene_extraction(self, script_content: str) -> List[Dict[str, Any]]:
        """Basic scene extraction as fallback"""
        scenes = []

        try:
            # Split by "Scene X:" pattern
            scene_sections = re.split(r"Scene\s+\d+:", script_content)

            for i, section in enumerate(scene_sections[1:], 1):  # Skip first empty section
                # Extract basic scene info
                lines = section.strip().split("\n")
                title = lines[0].strip() if lines else f"Scene {i}"

                # Find visual description
                visual_desc = ""
                narration = ""
                for line in lines:
                    if line.strip().startswith("Visual:"):
                        visual_desc = line.replace("Visual:", "").strip()
                    elif line.strip().startswith("Narration:"):
                        narration = line.replace("Narration:", "").strip()

                scene_data = {
                    "scene_number": i,
                    "title": title,
                    "location_group": 1,
                    "visual_description": visual_desc,
                    "narration_type": "Voice-over, Professional",
                    "narration": narration,
                    "sfx_music": "Professional background music",
                    "transition": "Smooth cut",
                    "duration": "5s",
                }
                scenes.append(scene_data)

        except Exception as e:
            logger.error(f"Failed to perform basic scene extraction: {e}")
            # Create a single default scene
            scenes = [
                {
                    "scene_number": 1,
                    "title": "Professional Advertisement Scene",
                    "location_group": 1,
                    "visual_description": "Professional advertisement setting",
                    "narration_type": "Voice-over, Professional",
                    "narration": "Professional advertisement narration",
                    "sfx_music": "Professional background music",
                    "transition": "Smooth cut",
                    "duration": "5s",
                }
            ]

        return scenes

    def _fallback_scene_extraction(self, script_content: str) -> List[Dict[str, Any]]:
        """Fallback scene extraction when all else fails"""
        logger.warning("Using fallback scene extraction")

        return [
            {
                "scene_number": 1,
                "title": "Professional Advertisement",
                "description": "Professional advertisement scene",
                "visual_description": "Professional advertisement setting with product placement",
                "narration": "Professional advertisement narration",
                "duration": "5s",
                "location_group": 1,
                "status": "active",
                "generation_status": "completed",
                "character_info": {
                    "main_character": "Professional advertisement character",
                    "description": "Professional advertisement character",
                    "type": "professional_advertisement",
                },
                "metadata": {
                    "sfx_music": "Professional background music",
                    "transition": "Smooth cut",
                    "script_title": "Professional Advertisement",
                    "script_overview": "Professional advertisement script",
                    "scene_type": "professional_advertisement",
                },
            }
        ]


# Create global instance
scene_processor = SceneProcessor()
