"""
JWT token management and security utilities
"""

# Global imports
import jwt
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
from fastapi import HTT<PERSON>Ex<PERSON>, status

# Local imports
from src.shared.config.settings import settings


class JWTManager:
    """JWT token management"""

    @staticmethod
    def create_access_token(data: Dict[str, Any]) -> str:
        """Create JWT access token"""
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(minutes=settings.jwt_access_token_expire_minutes)
        to_encode.update({"exp": expire})

        encoded_jwt = jwt.encode(
            to_encode, settings.jwt_secret_key, algorithm=settings.jwt_algorithm
        )
        return encoded_jwt

    @staticmethod
    def decode_token(token: str) -> Optional[Dict[str, Any]]:
        """Decode and validate JWT token"""
        try:
            payload = jwt.decode(
                token, settings.jwt_secret_key, algorithms=[settings.jwt_algorithm]
            )
            return payload
        except jwt.PyJWTError:
            return None

    @staticmethod
    def create_user_token(org_id: str, user_id: str, email: str) -> str:
        """Create token with user information"""
        return JWTManager.create_access_token(
            {"sub": email, "org_id": org_id, "user_id": user_id, "type": "access"}
        )

    @staticmethod
    def create_refresh_token(org_id: str, user_id: str, email: str) -> str:
        """Create refresh token with user information and longer expiry"""
        to_encode = {"sub": email, "org_id": org_id, "user_id": user_id, "type": "refresh"}
        expire = datetime.utcnow() + timedelta(days=7)  # 7 days expiry for refresh token
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(
            to_encode, settings.jwt_secret_key, algorithm=settings.jwt_algorithm
        )
        return encoded_jwt


def verify_token(token: str) -> Dict[str, Any]:
    """Verify token and return payload or raise exception"""
    payload = JWTManager.decode_token(token)
    if payload is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return payload
