version: '3.8'

services:
  api:
    build: .
    image: vidflux-backend:latest
    container_name: vidflux_api
    command: uvicorn main:app --host 0.0.0.0 --port 8000
    env_file:
      - .env
    ports:
      - "8000:8000"
    restart: always

  worker:
    build: .
    image: vidflux-backend:latest
    container_name: vidflux_worker
    command: celery -A src.worker.celery_app worker --loglevel=info
    env_file:
      - .env
    restart: always

# Note: No Redis or Postgres containers are defined here. Use your AWS RDS and (optionally) external Redis.
# Make sure your .env file contains all necessary environment variables for production.